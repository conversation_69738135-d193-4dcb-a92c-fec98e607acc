/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import yargs from 'yargs/yargs';
import { hideBin } from 'yargs/helpers';
import process from 'node:process';
import { Config, loadServerHierarchicalMemory, setArienMdFilename as setServerArienMdFilename, getCurrentArienMdFilename, ApprovalMode, ARIEN_CONFIG_DIR as ARIEN_DIR, DEFAULT_ARIEN_MODEL, DEFAULT_ARIEN_EMBEDDING_MODEL, FileDiscoveryService, } from '@arien/arien-cli-core';
import { getCliVersion } from '../utils/version.js';
import * as dotenv from 'dotenv';
import * as fs from 'node:fs';
import * as path from 'node:path';
import * as os from 'node:os';
import { loadSandboxConfig } from './sandboxConfig.js';
import { getBuiltInMcpServers, getBuiltInMcpServersSync } from './built-in-mcp-servers.js';
// Simple console logger for now - replace with actual logger if available
const logger = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    debug: (...args) => console.debug('[DEBUG]', ...args),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    warn: (...args) => console.warn('[WARN]', ...args),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    error: (...args) => console.error('[ERROR]', ...args),
};
async function parseArguments() {
    const argv = await yargs(hideBin(process.argv))
        .option('model', {
        alias: 'm',
        type: 'string',
        description: `Model`,
        default: process.env.ARIEN_MODEL || DEFAULT_ARIEN_MODEL,
    })
        .option('prompt', {
        alias: 'p',
        type: 'string',
        description: 'Prompt. Appended to input on stdin (if any).',
    })
        .option('sandbox', {
        alias: 's',
        type: 'boolean',
        description: 'Run in sandbox?',
    })
        .option('sandbox-image', {
        type: 'string',
        description: 'Sandbox image URI.',
    })
        .option('debug', {
        alias: 'd',
        type: 'boolean',
        description: 'Run in debug mode?',
        default: false,
    })
        .option('all_files', {
        alias: 'a',
        type: 'boolean',
        description: 'Include ALL files in context?',
        default: false,
    })
        .option('show_memory_usage', {
        type: 'boolean',
        description: 'Show memory usage in status bar',
        default: false,
    })
        .option('yolo', {
        alias: 'y',
        type: 'boolean',
        description: 'Automatically accept all actions (aka YOLO mode, see https://www.youtube.com/watch?v=xvFZjo5PgG0 for more details)?',
        default: false,
    })
        .option('telemetry', {
        type: 'boolean',
        description: 'Enable telemetry? This flag specifically controls if telemetry is sent. Other --telemetry-* flags set specific values but do not enable telemetry on their own.',
    })
        .option('telemetry-target', {
        type: 'string',
        choices: ['local', 'gcp'],
        description: 'Set the telemetry target (local or gcp). Overrides settings files.',
    })
        .option('telemetry-otlp-endpoint', {
        type: 'string',
        description: 'Set the OTLP endpoint for telemetry. Overrides environment variables and settings files.',
    })
        .option('telemetry-log-prompts', {
        type: 'boolean',
        description: 'Enable or disable logging of user prompts for telemetry. Overrides settings files.',
    })
        .option('checkpointing', {
        alias: 'c',
        type: 'boolean',
        description: 'Enables checkpointing of file edits',
        default: true,
    })
        .version(await getCliVersion()) // This will enable the --version flag based on package.json
        .alias('v', 'version')
        .help()
        .alias('h', 'help')
        .strict().argv;
    return argv;
}
// This function is now a thin wrapper around the server's implementation.
// It's kept in the CLI for now as App.tsx directly calls it for memory refresh.
// TODO: Consider if App.tsx should get memory via a server call or if Config should refresh itself.
export async function loadHierarchicalArienMemory(currentWorkingDirectory, debugMode, fileService, extensionContextFilePaths = []) {
    if (debugMode) {
        logger.debug(`CLI: Delegating hierarchical memory load to server for CWD: ${currentWorkingDirectory}`);
    }
    // Directly call the server function.
    // The server function will use its own homedir() for the global path.
    return loadServerHierarchicalMemory(currentWorkingDirectory, debugMode, fileService, extensionContextFilePaths);
}
export async function loadCliConfig(settings, extensions, sessionId) {
    loadEnvironment();
    const argv = await parseArguments();
    const debugMode = argv.debug || false;
    // Set the context filename in the server's memoryTool module BEFORE loading memory
    // TODO(b/343434939): This is a bit of a hack. The contextFileName should ideally be passed
    // directly to the Config constructor in core, and have core handle setArienMdFilename.
    // However, loadHierarchicalArienMemory is called *before* createServerConfig.
    if (settings.contextFileName) {
        setServerArienMdFilename(settings.contextFileName);
    }
    else {
        // Reset to default if not provided in settings.
        setServerArienMdFilename(getCurrentArienMdFilename());
    }
    const extensionContextFilePaths = extensions.flatMap((e) => e.contextFiles);
    const fileService = new FileDiscoveryService(process.cwd());
    // Call the (now wrapper) loadHierarchicalArienMemory which calls the server's version
    const { memoryContent, fileCount } = await loadHierarchicalArienMemory(process.cwd(), debugMode, fileService, extensionContextFilePaths);
    const mcpServers = await mergeMcpServers(settings, extensions);
    const sandboxConfig = await loadSandboxConfig(settings, argv);
    return new Config({
        sessionId,
        embeddingModel: DEFAULT_ARIEN_EMBEDDING_MODEL,
        sandbox: sandboxConfig,
        targetDir: process.cwd(),
        debugMode,
        question: argv.prompt || '',
        fullContext: argv.all_files || false,
        coreTools: settings.coreTools || undefined,
        excludeTools: settings.excludeTools || undefined,
        toolDiscoveryCommand: settings.toolDiscoveryCommand,
        toolCallCommand: settings.toolCallCommand,
        mcpServerCommand: settings.mcpServerCommand,
        mcpServers,
        userMemory: memoryContent,
        arienMdFileCount: fileCount,
        approvalMode: argv.yolo || false ? ApprovalMode.YOLO : ApprovalMode.DEFAULT,
        showMemoryUsage: argv.show_memory_usage || settings.showMemoryUsage || false,
        accessibility: settings.accessibility,
        telemetry: {
            enabled: argv.telemetry ?? settings.telemetry?.enabled,
            target: (argv.telemetryTarget ??
                settings.telemetry?.target),
            otlpEndpoint: argv.telemetryOtlpEndpoint ??
                process.env.OTEL_EXPORTER_OTLP_ENDPOINT ??
                settings.telemetry?.otlpEndpoint,
            logPrompts: argv.telemetryLogPrompts ?? settings.telemetry?.logPrompts,
        },
        usageStatisticsEnabled: settings.usageStatisticsEnabled ?? true,
        // Git-aware file filtering settings
        fileFiltering: {
            respectGitIgnore: settings.fileFiltering?.respectGitIgnore,
            enableRecursiveFileSearch: settings.fileFiltering?.enableRecursiveFileSearch,
        },
        checkpointing: argv.checkpointing ?? settings.checkpointing?.enabled,
        proxy: process.env.HTTPS_PROXY ||
            process.env.https_proxy ||
            process.env.HTTP_PROXY ||
            process.env.http_proxy,
        cwd: process.cwd(),
        fileDiscoveryService: fileService,
        bugCommand: settings.bugCommand,
        model: argv.model,
        extensionContextFilePaths,
    });
}
/**
 * Validates an MCP server configuration to ensure it has the required properties.
 */
function validateMcpServerConfig(serverName, config) {
    if (!config) {
        logger.warn(`MCP server "${serverName}" has no configuration. Skipping.`);
        return false;
    }
    // Check if at least one transport method is configured
    const hasStdio = config.command;
    const hasSSE = config.url;
    const hasHTTP = config.httpUrl;
    const hasTCP = config.tcp;
    if (!hasStdio && !hasSSE && !hasHTTP && !hasTCP) {
        logger.warn(`MCP server "${serverName}" has no valid transport configuration (missing command, url, httpUrl, or tcp). Skipping.`);
        return false;
    }
    return true;
}
/**
 * Safely adds an MCP server to the collection with validation and error handling.
 */
function addMcpServer(mcpServers, serverName, serverConfig, source) {
    try {
        if (!validateMcpServerConfig(serverName, serverConfig)) {
            return;
        }
        if (mcpServers[serverName]) {
            logger.debug(`Overriding MCP server "${serverName}" with ${source} configuration.`);
        }
        else {
            logger.debug(`Adding MCP server "${serverName}" from ${source}.`);
        }
        mcpServers[serverName] = serverConfig;
    }
    catch (error) {
        logger.error(`Failed to add MCP server "${serverName}" from ${source}: ${error}`);
    }
}
async function mergeMcpServers(settings, extensions) {
    const mcpServers = {};
    try {
        // Start with built-in MCP servers (lowest priority) - unless disabled
        const enableBuiltIn = settings.enableBuiltInMcpServers !== false; // Default to true
        if (enableBuiltIn) {
            try {
                // Use the new async validation system
                const builtInServers = await getBuiltInMcpServers();
                logger.debug(`Loading ${Object.keys(builtInServers).length} validated built-in MCP servers.`);
                Object.entries(builtInServers).forEach(([key, server]) => {
                    addMcpServer(mcpServers, key, server, 'built-in');
                });
            }
            catch (error) {
                logger.warn(`Failed to load validated built-in servers, falling back to sync version: ${error}`);
                // Fallback to synchronous version
                const fallbackServers = getBuiltInMcpServersSync();
                logger.debug(`Loading ${Object.keys(fallbackServers).length} fallback built-in MCP servers.`);
                Object.entries(fallbackServers).forEach(([key, server]) => {
                    addMcpServer(mcpServers, key, server, 'built-in');
                });
            }
        }
        else {
            logger.debug('Built-in MCP servers are disabled by user configuration.');
        }
        // Add extension MCP servers (medium priority)
        for (const extension of extensions) {
            const extensionServers = extension.config.mcpServers || {};
            if (Object.keys(extensionServers).length > 0) {
                logger.debug(`Loading ${Object.keys(extensionServers).length} MCP servers from extension "${extension.config.name}".`);
            }
            Object.entries(extensionServers).forEach(([key, server]) => {
                addMcpServer(mcpServers, key, server, `extension "${extension.config.name}"`);
            });
        }
        // Add user settings MCP servers (highest priority)
        const userServers = settings.mcpServers || {};
        if (Object.keys(userServers).length > 0) {
            logger.debug(`Loading ${Object.keys(userServers).length} MCP servers from user settings.`);
        }
        Object.entries(userServers).forEach(([key, server]) => {
            addMcpServer(mcpServers, key, server, 'user settings');
        });
        const totalServers = Object.keys(mcpServers).length;
        logger.debug(`Total MCP servers configured: ${totalServers}`);
        return mcpServers;
    }
    catch (error) {
        logger.error(`Error merging MCP servers: ${error}`);
        // Return at least the built-in servers as fallback (if enabled)
        const enableBuiltIn = settings.enableBuiltInMcpServers !== false;
        if (enableBuiltIn) {
            try {
                const fallbackServers = getBuiltInMcpServersSync();
                logger.warn(`Falling back to built-in MCP servers only (${Object.keys(fallbackServers).length} servers).`);
                return fallbackServers;
            }
            catch (fallbackError) {
                logger.error(`Failed to load fallback MCP servers: ${fallbackError}`);
                return {};
            }
        }
        else {
            logger.warn('Built-in MCP servers are disabled, returning empty configuration.');
            return {};
        }
    }
}
function findEnvFile(startDir) {
    let currentDir = path.resolve(startDir);
    while (true) {
        // prefer arien-specific .env under ARIEN_DIR
        const arienEnvPath = path.join(currentDir, ARIEN_DIR, '.env');
        if (fs.existsSync(arienEnvPath)) {
            return arienEnvPath;
        }
        const envPath = path.join(currentDir, '.env');
        if (fs.existsSync(envPath)) {
            return envPath;
        }
        const parentDir = path.dirname(currentDir);
        if (parentDir === currentDir || !parentDir) {
            // check .env under home as fallback, again preferring arien-specific .env
            const homeArienEnvPath = path.join(os.homedir(), ARIEN_DIR, '.env');
            if (fs.existsSync(homeArienEnvPath)) {
                return homeArienEnvPath;
            }
            const homeEnvPath = path.join(os.homedir(), '.env');
            if (fs.existsSync(homeEnvPath)) {
                return homeEnvPath;
            }
            return null;
        }
        currentDir = parentDir;
    }
}
export function loadEnvironment() {
    const envFilePath = findEnvFile(process.cwd());
    if (envFilePath) {
        dotenv.config({ path: envFilePath, quiet: true });
    }
}
//# sourceMappingURL=config.js.map